"use strict";(()=>{var e={};e.id=813,e.ids=[813],e.modules={145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},1467:e=>{e.exports=import("@vercel/kv")},6249:(e,a)=>{Object.defineProperty(a,"l",{enumerable:!0,get:function(){return function e(a,n){return n in a?a[n]:"then"in a&&"function"==typeof a.then?a.then(a=>e(a,n)):"function"==typeof a&&"default"===n?a:void 0}}})},4181:(e,a,n)=>{n.a(e,async(e,i)=>{try{n.r(a),n.d(a,{config:()=>p,default:()=>t,routeModule:()=>l});var m=n(1802),g=n(7153),s=n(6249),o=n(9485),r=e([o]);o=(r.then?(await r)():r)[0];let t=(0,s.l)(o,"default"),p=(0,s.l)(o,"config"),l=new m.PagesAPIRouteModule({definition:{kind:g.x.PAGES_API,page:"/api/get-mix-chars",pathname:"/api/get-mix-chars",bundlePath:"",filename:""},userland:o});i()}catch(e){i(e)}})},94:(e,a,n)=>{n.d(a,{kl:()=>i});let i=[{name:"Birdo",image:"/imagens/birdo.jpg"},{name:"Dry Bones",image:"/imagens/dry.jpg"},{name:"Dixie Kong",image:"/imagens/dixie.webp"},{name:"Pauline",image:"/imagens/pauline.jpg"},{name:"Rosalina",image:"/imagens/rosalina.png"},{name:"Kamek",image:"/imagens/kamek.png"},{name:"Koopa",image:"/imagens/koopa.png"},{name:"Petey Piranha",image:"/imagens/petey.webp"},{name:"Goomba",image:"/imagens/goomba.jpg"},{name:"Toadette",image:"/imagens/toadette.png"},{name:"James",image:"/imagens/james.webp"},{name:"Piramid Head",image:"/imagens/piramid.webp"},{name:"Skull Kid",image:"/imagens/skull.jpg"},{name:"Krystal",image:"/imagens/kristal.webp"},{name:"Hammer Bro",image:"/imagens/hammer.jpg"},{name:"Guile",image:"/imagens/guile.jpg"},{name:"Ghost Pac-man",image:"/imagens/ghost.webp"},{name:"Chaim Chomp",image:"/imagens/chaim.webp"},{name:"Rain World",image:"/imagens/rain.jpg"},{name:"Niko",image:"/imagens/niko.jpg"},{name:"Mita",image:"/imagens/mita.jpg"},{name:"Mineirinho",image:"/imagens/mineirinho.jpg"},{name:"Little Nightmares",image:"/imagens/little.jpg"},{name:"Isaac",image:"/imagens/isac.jpg"},{name:"Among Us",image:"/imagens/among.webp"},{name:"Leon",image:"/imagens/leon.jpg"},{name:"Linus",image:"/imagens/linus.jpg"},{name:"Robin",image:"/imagens/robin.jpg"},{name:"Sebastian",image:"/imagens/sebastian.webp"},{name:"Abigail",image:"/imagens/abigail.webp"},{name:"Lewis",image:"/imagens/lewis.webp"},{name:"Ratchet",image:"/imagens/ratchet.jpg"},{name:"Klonoa",image:"/imagens/klonoa.jpg"},{name:"Meta Knight",image:"/imagens/meta.jpg"},{name:"Foxy",image:"/imagens/foxy.jpg"},{name:"Bonnie",image:"/imagens/bonnie.webp"},{name:"Chica",image:"/imagens/chica.webp"},{name:"Freddy",image:"/imagens/freddy.webp"},{name:"Inside",image:"/imagens/inside.jpg"},{name:"Gris",image:"/imagens/gris.png"},{name:"Demonio",image:"/imagens/demonio.webp"},{name:"Caneco",image:"/imagens/caneco.webp"},{name:"Tiny Tiger",image:"/imagens/tinytiger.jpg"},{name:"Coco",image:"/imagens/coco.webp"},{name:"Cortex",image:"/imagens/cortex.jpg"},{name:"Knight",image:"/imagens/knight.jpeg"},{name:"Macaco Engenheiro",image:"/imagens/macaco.webp"},{name:"Mae",image:"/imagens/mae.webp"},{name:"Blaze",image:"/imagens/blaze.webp"},{name:"Rouge",image:"/imagens/rouge.jpg"},{name:"Amy",image:"/imagens/amy.webp"},{name:"Absol",image:"/imagens/absol.webp"},{name:"Umbreon",image:"/imagens/umbreon.webp"},{name:"Espeon",image:"/imagens/espeon.webp"},{name:"Flareon",image:"/imagens/flareon.jpg"},{name:"Jolteon",image:"/imagens/jolteon.webp"},{name:"Vaporeon",image:"/imagens/vaporeon.jpeg"},{name:"Ditto",image:"/imagens/ditto.webp"},{name:"Raichu",image:"/imagens/raichu.jpg"},{name:"Dragonite",image:"/imagens/dragonite.jpg"},{name:"Blastoise",image:"/imagens/blastoise.jpeg"},{name:"Snorlax",image:"/imagens/snorlax.jpeg"},{name:"Gardevoir",image:"/imagens/gardevoir.webp"},{name:"Garchomp",image:"/imagens/garchomp.webp"},{name:"Lucario",image:"/imagens/lucario.jpg"},{name:"Mew",image:"/imagens/mew.jpg"},{name:"Squirtle",image:"/imagens/squirtle.jpeg"},{name:"Charmander",image:"/imagens/charmander.webp"},{name:"Bulbasaur",image:"/imagens/bulbasaur.webp"},{name:"Eevee",image:"/imagens/eevee.jpg"},{name:"Charizard",image:"/imagens/charizard.jpg"},{name:"Tommy Vercetti",image:"/imagens/tommy_vercetti.webp"},{name:"Shy Guy",image:"/imagens/shy_guy.webp"},{name:"Papyrus",image:"/imagens/papyrus.jpg"},{name:"Little Mac",image:"/imagens/little_mac.webp"},{name:"Liu Kang",image:"/imagens/liu_kang.jpg"},{name:"King Boo",image:"/imagens/king_boo.png"},{name:"Knuckles",image:"/imagens/knuckles.jpg"},{name:"King Dedede",image:"/imagens/king_dedede.webp"},{name:"Duke Nukem",image:"/imagens/duke_nukem.webp"},{name:"Diddy Kong",image:"/imagens/diddy_kong.png"},{name:"Cranky Kong",image:"/imagens/cranky_kong.png"},{name:"Conker",image:"/imagens/conker.jpg"},{name:"Sub Zero",image:"/imagens/subzero.jpg"},{name:"Scorpion",image:"/imagens/scorpion.png"},{name:"Captain Toad",image:"/imagens/captain_toad.png"},{name:"Shantae",image:"/imagens/shantae.jpeg"},{name:"Geno",image:"/imagens/geno.webp"},{name:"Funky Kong",image:"/imagens/funky_kong.jpeg"},{name:"Silver",image:"/imagens/silver.webp"},{name:"Joker",image:"/imagens/joker.jpg"},{name:"Piranha Plant",image:"/imagens/piranha_plant.webp"},{name:"Incineroar",image:"/imagens/incineroar.jpeg"},{name:"King K. Rool",image:"/imagens/king_k_rool.jpg"},{name:"Inkling",image:"/imagens/inkling.webp"},{name:"Cloud",image:"/imagens/cloud.webp"},{name:"Ken",image:"/imagens/ken.jpeg"},{name:"Duck Hunt",image:"/imagens/duck_hunt.png"},{name:"Bowser Jr.",image:"/imagens/bowser_jr.jpg"}].filter((e,a,n)=>a===n.findIndex(a=>a.name===e.name))},9485:(e,a,n)=>{n.a(e,async(e,i)=>{try{n.r(a),n.d(a,{default:()=>s});var m=n(1467);n(94);var g=e([m]);async function s(e,a){if("GET"!==e.method)return a.status(405).json({message:"M\xe9todo N\xe3o Permitido. Esperado GET."});try{let{category:n}=e.query;if(!n||!["mix1","mix2","mix3"].includes(n))return a.status(400).json({message:"Categoria inv\xe1lida. Use mix1, mix2 ou mix3."});console.log(`Buscando personagens para ${n}...`);let i=await m.kv.get(`${n}_characters`);if(!i)return console.warn(`Nenhum dado encontrado para ${n}`),a.status(404).json({message:`Nenhum dado encontrado para ${n}. \xc9 necess\xe1rio gerar os mixes primeiro.`,needsGeneration:!0});if(!Array.isArray(i))throw Error(`Dados inv\xe1lidos para ${n}: n\xe3o \xe9 um array`);let g=["name","image"];for(let e of i){if(!e||"object"!=typeof e)throw Error(`Dados inv\xe1lidos para ${n}: elemento n\xe3o \xe9 um objeto v\xe1lido`);for(let a of g)if(!(a in e))throw Error(`Dados inv\xe1lidos para ${n}: personagem sem a propriedade ${a}`)}return console.log(`Retornando ${i.length} personagens para ${n}`),a.status(200).json(i)}catch(e){return console.error("Erro ao buscar personagens:",e),a.status(500).json({message:"Erro ao buscar personagens.",error:e.message})}}m=(g.then?(await g)():g)[0],i()}catch(e){i(e)}})},7153:(e,a)=>{var n;Object.defineProperty(a,"x",{enumerable:!0,get:function(){return n}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(n||(n={}))},1802:(e,a,n)=>{e.exports=n(145)}};var a=require("../../webpack-api-runtime.js");a.C(e);var n=a(a.s=4181);module.exports=n})();