"use strict";(()=>{var e={};e.id=687,e.ids=[687],e.modules={145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6249:(e,r)=>{Object.defineProperty(r,"l",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},6323:(e,r,t)=>{t.r(r),t.d(r,{config:()=>f,default:()=>c,routeModule:()=>p});var n={};t.r(n),t.d(n,{default:()=>P});var i=t(1802),o=t(7153),u=t(6249);let a=require("fs");var s=t.n(a);let d=require("path");var l=t.n(d);function P(e,r){try{let e=l().join(process.cwd(),"public","index.html"),t=s().readFileSync(e,"utf8");r.setHeader("Content-Type","text/html"),r.status(200).send(t)}catch(e){console.error("Erro ao servir HTML:",e),r.status(500).json({error:"Erro interno do servidor"})}}let c=(0,u.l)(n,"default"),f=(0,u.l)(n,"config"),p=new i.PagesAPIRouteModule({definition:{kind:o.x.PAGES_API,page:"/api/serve-html",pathname:"/api/serve-html",bundlePath:"",filename:""},userland:n})},7153:(e,r)=>{var t;Object.defineProperty(r,"x",{enumerable:!0,get:function(){return t}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(t||(t={}))},1802:(e,r,t)=>{e.exports=t(145)}};var r=require("../../webpack-api-runtime.js");r.C(e);var t=r(r.s=6323);module.exports=t})();