"use strict";(()=>{var e={};e.id=318,e.ids=[318],e.modules={145:e=>{e.exports=require("next/dist/compiled/next-server/pages-api.runtime.prod.js")},6249:(e,t)=>{Object.defineProperty(t,"l",{enumerable:!0,get:function(){return function e(t,n){return n in t?t[n]:"then"in t&&"function"==typeof t.then?t.then(t=>e(t,n)):"function"==typeof t&&"default"===n?t:void 0}}})},1359:(e,t,n)=>{n.r(t),n.d(t,{config:()=>d,default:()=>P,routeModule:()=>s});var r={};n.r(r),n.d(r,{default:()=>a});var i=n(1802),u=n(7153),o=n(6249);function a(e,t){t.status(200).json({message:"API funcionando!"})}let P=(0,o.l)(r,"default"),d=(0,o.l)(r,"config"),s=new i.PagesAPIRouteModule({definition:{kind:u.x.PAGES_API,page:"/api/test",pathname:"/api/test",bundlePath:"",filename:""},userland:r})},7153:(e,t)=>{var n;Object.defineProperty(t,"x",{enumerable:!0,get:function(){return n}}),function(e){e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE"}(n||(n={}))},1802:(e,t,n)=>{e.exports=n(145)}};var t=require("../../webpack-api-runtime.js");t.C(e);var n=t(t.s=1359);module.exports=n})();